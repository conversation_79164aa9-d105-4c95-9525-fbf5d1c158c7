#!/usr/bin/env python3

"""调试LeapHand机器人的关节索引名称"""

import argparse
from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="调试LeapHand机器人的关节索引名称")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import torch
from isaaclab.assets import Articulation
from isaaclab.scene import InteractiveScene, InteractiveSceneCfg
from isaaclab.sim import SimulationCfg, SimulationContext
from isaaclab.utils import configclass
from leaphand.robots.leap import LEAP_HAND_CFG
import isaaclab.sim as sim_utils
from isaaclab.assets import AssetBaseCfg


@configclass
class DebugSceneCfg(InteractiveSceneCfg):
    """LeapHand调试场景配置"""
    
    # 地面
    ground = AssetBaseCfg(
        prim_path="/World/defaultGroundPlane",
        spawn=sim_utils.GroundPlaneCfg(),
    )
    
    # 灯光
    dome_light = AssetBaseCfg(
        prim_path="/World/Light",
        spawn=sim_utils.DomeLightCfg(intensity=3000.0, color=(0.75, 0.75, 0.75))
    )
    
    # LeapHand机器人
    robot = LEAP_HAND_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")


def main():
    """主函数"""
    
    # 创建仿真配置
    sim_cfg = SimulationCfg(
        dt=1.0 / 60.0,
        render_interval=1,
    )
    
    # 创建仿真上下文
    sim = SimulationContext(sim_cfg)
    
    # 设置场景配置
    scene_cfg = DebugSceneCfg(num_envs=1, env_spacing=1.0)
    scene = InteractiveScene(scene_cfg)
    
    # 设计场景
    scene.reset()
    
    # 获取机器人对象
    robot = scene["robot"]
    
    print("=" * 60)
    print("LeapHand 关节索引名称调试信息")
    print("=" * 60)
    
    # 打印基本信息
    print(f"机器人资产路径: {scene_cfg.robot.spawn.usd_path}")
    print(f"总关节数量: {robot.num_joints}")
    print(f"执行器关节数量: {robot.num_actions}")
    
    print("\n" + "=" * 60)
    print("关节名称列表")
    print("=" * 60)
    
    # 打印所有关节名称和索引
    joint_names = robot.joint_names
    for i, joint_name in enumerate(joint_names):
        print(f"索引 {i:2d}: {joint_name}")
    
    print("\n" + "=" * 60)
    print("关节位置限制")
    print("=" * 60)
    
    # 打印关节位置限制
    joint_pos_limits = robot.root_physx_view.get_dof_limits()
    for i, joint_name in enumerate(joint_names):
        lower_limit = joint_pos_limits[0][0][i].item()
        upper_limit = joint_pos_limits[1][0][i].item()
        print(f"{joint_name:20s}: [{lower_limit:8.4f}, {upper_limit:8.4f}]")
    
    print("\n" + "=" * 60)
    print("当前关节位置")
    print("=" * 60)
    
    # 打印当前关节位置
    joint_pos = robot.data.joint_pos
    for i, joint_name in enumerate(joint_names):
        pos = joint_pos[0][i].item()
        print(f"{joint_name:20s}: {pos:8.4f}")
    
    print("\n" + "=" * 60)
    print("执行器配置")
    print("=" * 60)
    
    # 打印执行器配置信息
    for actuator_name, actuator in robot.actuators.items():
        print(f"执行器名称: {actuator_name}")
        print(f"  关节名称表达式: {actuator.cfg.joint_names_expr}")
        print(f"  控制的关节数量: {len(actuator.joint_indices)}")
        print(f"  关节索引: {actuator.joint_indices}")
        if len(actuator.joint_indices) <= 20:  # 只显示前20个关节名称
            controlled_joints = [joint_names[i] for i in actuator.joint_indices]
            print(f"  控制的关节名称: {controlled_joints}")
        print(f"  力限制: {actuator.cfg.effort_limit}")
        print(f"  速度限制: {actuator.cfg.velocity_limit}")
        print(f"  刚度: {actuator.cfg.stiffness}")
        print(f"  阻尼: {actuator.cfg.damping}")
        print()
    
    print("\n" + "=" * 60)
    print("关节名称分组分析")
    print("=" * 60)
    
    # 分析关节名称的模式
    finger_joints = {}
    other_joints = []
    
    for i, joint_name in enumerate(joint_names):
        # 尝试识别手指关节模式
        if 'thumb' in joint_name.lower():
            finger = 'thumb'
        elif 'index' in joint_name.lower():
            finger = 'index'
        elif 'middle' in joint_name.lower():
            finger = 'middle'
        elif 'ring' in joint_name.lower():
            finger = 'ring'
        elif 'pinky' in joint_name.lower() or 'little' in joint_name.lower():
            finger = 'pinky'
        else:
            # 尝试从关节名称中提取手指信息
            parts = joint_name.lower().split('_')
            if len(parts) >= 2:
                finger = parts[0] if parts[0] in ['thumb', 'index', 'middle', 'ring', 'pinky'] else 'unknown'
            else:
                finger = 'unknown'
        
        if finger != 'unknown':
            if finger not in finger_joints:
                finger_joints[finger] = []
            finger_joints[finger].append((i, joint_name))
        else:
            other_joints.append((i, joint_name))
    
    # 按手指分组显示
    for finger, joints in finger_joints.items():
        print(f"{finger.capitalize()} 手指关节:")
        for idx, name in joints:
            print(f"  索引 {idx:2d}: {name}")
        print()
    
    if other_joints:
        print("其他关节:")
        for idx, name in other_joints:
            print(f"  索引 {idx:2d}: {name}")
    
    print("\n" + "=" * 60)
    print("关节名称匹配测试")
    print("=" * 60)
    
    # 测试常用的关节名称模式匹配
    test_patterns = [
        "a_.*",  # 配置文件中使用的模式
        ".*thumb.*",
        ".*index.*", 
        ".*middle.*",
        ".*ring.*",
        ".*pinky.*",
    ]
    
    import re
    for pattern in test_patterns:
        regex = re.compile(pattern)
        matching_joints = [name for name in joint_names if regex.match(name)]
        print(f"模式 '{pattern}' 匹配的关节:")
        if matching_joints:
            for joint in matching_joints:
                print(f"  {joint}")
        else:
            print("  无匹配")
        print()


if __name__ == "__main__":
    main()
    # close sim app
    simulation_app.close()
