#!/usr/bin/env python3

"""Launch Isaac Sim Simulator first."""

import argparse
from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="通过设定初始关节位置来测试LeapHand机器人的关节索引名称及关节上下限")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import torch
import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation
from isaaclab.scene import InteractiveScene, InteractiveSceneCfg
from isaaclab.sim import SimulationCfg, SimulationContext
from leaphand.robots.leap import LEAP_HAND_CFG
from isaaclab.assets import ArticulationCfg, AssetBaseCfg
from isaaclab.utils import configclass

@configclass
class LeapHandTestSceneCfg(InteractiveSceneCfg):
    """LeapHand测试场景配置"""
    
    # 地面
    ground = AssetBaseCfg(
        prim_path="/World/defaultGroundPlane",
        spawn=sim_utils.GroundPlaneCfg(),
    )
    
    # 灯光
    dome_light = AssetBaseCfg(
        prim_path="/World/Light",
        spawn=sim_utils.DomeLightCfg(intensity=3000.0, color=(0.75, 0.75, 0.75))
    )
    
    # LeapHand机器人
    leap: ArticulationCfg = LEAP_HAND_CFG.replace(
        prim_path="/World/Robot",
        init_state=ArticulationCfg.InitialStateCfg(
            pos=(0.0, 0.0, 0.5),  # pos,rot场景初始化时便被应用
            rot=(0.5, 0.5, -0.5, 0.5),
            joint_pos={ 
                "a_0": 0.0, "a_1": 0.0, "a_2": 0.0, "a_3": 0.0,
                "a_4": 0.0, "a_5": 0.0, "a_6": 0.0, "a_7": 0.0,
                "a_8": 0.0, "a_9": 0.0, "a_10": 0.0, "a_11": 0.0,
                "a_12": 1.0, "a_13": 0.0, "a_14": 0.0, "a_15": 0.0
            },
            joint_vel={"a_.*": 0.0},
        )
    )

def run_simple_simulator(sim: sim_utils.SimulationContext, scene: InteractiveScene):
    """Runs the simulation loop 仅画面渲染."""
    # Extract scene entities
    # note: we only do this here for readability.
    # robot = scene["leap"]
    # data = robot.data
    
    # Define simulation stepping
    while simulation_app.is_running():
        # perform step
        sim.step()

def run_goal_reaching_simulator(sim: sim_utils.SimulationContext, scene: InteractiveScene):
    """Runs the simulation loop 设定关节角度."""
    pass

def main():
    """主函数"""
    
    # Create the simulation context
    sim = SimulationContext(SimulationCfg(dt=1.0 / 60.0, render_interval=1))
    
    # Create the scene
    scene_cfg = LeapHandTestSceneCfg(num_envs=1, env_spacing=1.0)
    scene = InteractiveScene(scene_cfg)
    
    # Play the simulator
    sim.reset()
    
    # 获取机器人资产
    robot = scene["leap"]
    data = robot.data
    robot.write_joint_state_to_sim(data.default_joint_pos, data.default_joint_vel)

    # 获取关节名称和索引
    joint_names = robot.joint_names # 这个也可以用data.joint_names
    
    # 获取关节上下限
    joint_limits = data.default_joint_limits[0,:] # 因为default_joint_limits是(num_envs, num_joints, 2)的形状
    
    # 打印关节名称和索引
    print("关节名称和索引:")
    for i, joint_name in enumerate(joint_names):
        print(f" 关节索引: {i}: 关节名称: {joint_name}")
    
    # 打印关节上下限
    print("关节上下限:")
    for joint_name, joint_limit in zip(joint_names, joint_limits):
        print(f"关节名称: {joint_name}, 关节上下限: {joint_limit}")
    
    run_simple_simulator(sim, scene)

if __name__ == "__main__":
    main()
    simulation_app.close() # 这个不加就会直接关闭